{"name": "pro-equalizer", "displayName": "Pro equalizer", "version": "0.0.1", "description": "Pro Equalizer", "author": "Viiny Lab", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^3.1.0", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.7"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "postcss": "8.4.33", "prettier": "3.2.4", "typescript": "5.3.3"}, "manifest": {"host_permissions": ["https://*/*"]}}